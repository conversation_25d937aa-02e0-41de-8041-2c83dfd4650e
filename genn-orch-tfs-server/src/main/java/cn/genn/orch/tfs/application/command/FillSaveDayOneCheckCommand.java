package cn.genn.orch.tfs.application.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
public class FillSaveDayOneCheckCommand {
    @ApiModelProperty("飞书用户OpenId")
    @NotBlank(message = "openId不能为空")
    private String openId;

    @ApiModelProperty("填报日期")
    @NotNull(message = "填报日期不能为空")
    private LocalDate day;
}
