package cn.genn.orch.tfs.infrastructure.mapper;

import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */

public interface TfsDayFillLogMapper extends BaseMapper<TfsDayFillLogPO> {

    default TfsDayFillLogPO selectByOpenIdAndDay(String openId, LocalDate day){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .eq(TfsDayFillLogPO::getOpenId, openId)
                .eq(TfsDayFillLogPO::getSendDate, day)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default List<TfsDayFillLogPO> selectInfo(LocalDate start, LocalDate end, String status){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .between(ObjUtil.isNotEmpty(start) && ObjUtil.isNotEmpty(end), TfsDayFillLogPO::getSendDate, start, end)
                .eq(ObjUtil.isNotEmpty(status), TfsDayFillLogPO::getStatus, status);
        return selectList(wrapper);
    }

    default List<TfsDayFillLogPO> selectByToday(List<String> names){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .in(TfsDayFillLogPO::getName, names)
                .eq(TfsDayFillLogPO::getSendDate, LocalDate.now())
                .eq(TfsDayFillLogPO::getStatus, StatusEnum.PROJECT);
        return selectList(wrapper);
    }

    default TfsDayFillLogPO selectLastByOpenIdAndNoDay(String openId, LocalDate day) {
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .eq(TfsDayFillLogPO::getOpenId, openId)
                .ne(TfsDayFillLogPO::getSendDate, day)
                .orderByDesc(TfsDayFillLogPO::getSendDate)
                .last("limit 1");
        return selectOne(wrapper);
    }

    /**
     * 查询指定日期未填报的人员
     * @param day 指定日期
     * @return 未填报人员列表
     */
    default List<TfsDayFillLogPO> selectUnfilledUsersByDay(LocalDate day) {
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .eq(TfsDayFillLogPO::getSendDate, day)
                .eq(TfsDayFillLogPO::getStatus, StatusEnum.PROJECT);
        return selectList(wrapper);
    }
}
