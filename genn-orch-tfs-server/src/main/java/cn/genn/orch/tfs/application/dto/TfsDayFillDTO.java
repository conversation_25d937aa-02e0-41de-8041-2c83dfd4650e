package cn.genn.orch.tfs.application.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * TfsDayFillDTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TfsDayFillDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @ExcelProperty(value = "ID", index = 0)
    @ColumnWidth(15)
    private Long id;

    @ApiModelProperty(value = "日报日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "工时日期", index = 1)
    @ColumnWidth(15)
    private LocalDate fillDate;

    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名", index = 2)
    @ColumnWidth(15)
    private String name;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 3)
    @ColumnWidth(25)
    private String projectName;

    @ApiModelProperty(value = "工时（h）")
    @ExcelProperty(value = "工时(小时)", index = 4)
    @ColumnWidth(12)
    private Float workHour;

    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "人员所属部门", index = 5) // 设置列名和顺序
    @ColumnWidth(30) // 设置列宽
    private String departments;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间", index = 6)
    @ColumnWidth(25)
    private LocalDateTime createTime;

}

