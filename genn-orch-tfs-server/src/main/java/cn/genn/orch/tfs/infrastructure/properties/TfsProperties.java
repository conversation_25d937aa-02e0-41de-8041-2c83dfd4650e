package cn.genn.orch.tfs.infrastructure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "genn.tfs")
@RefreshScope
public class TfsProperties {

    //统一发送的白名单,输入名称
    private List<String> writeName;

    //飞书配置
    @NestedConfigurationProperty
    private FeishuProperties feishu;

    //操作多维表格配置
    @NestedConfigurationProperty
    private WikProperties wik;
    //卡片通知
    @NestedConfigurationProperty
    private CardProperties cardSend;


}
