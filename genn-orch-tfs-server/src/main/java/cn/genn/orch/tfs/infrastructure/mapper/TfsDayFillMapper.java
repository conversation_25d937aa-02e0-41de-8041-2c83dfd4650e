package cn.genn.orch.tfs.infrastructure.mapper;

import cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TfsDayFillMapper extends BaseMapper<TfsDayFillPO> {


    default void deleteByOpenIdAndDay(String openId, LocalDate day) {
        LambdaQueryWrapper<TfsDayFillPO> wrapper = Wrappers.lambdaQuery(TfsDayFillPO.class).eq(TfsDayFillPO::getOpenId, openId).eq(TfsDayFillPO::getFillDate, day);
        delete(wrapper);
    }

    default List<TfsDayFillPO> selectByOpenIdAndDay(String openId, LocalDate day) {
        LambdaQueryWrapper<TfsDayFillPO> wrapper = Wrappers.lambdaQuery(TfsDayFillPO.class).eq(TfsDayFillPO::getOpenId, openId).eq(TfsDayFillPO::getFillDate, day);
        return selectList(wrapper);
    }

    default List<TfsDayFillPO> selectFillInfo(LocalDate start, LocalDate end) {
        LambdaQueryWrapper<TfsDayFillPO> wrapper = Wrappers.lambdaQuery(TfsDayFillPO.class)
                .between(ObjUtil.isNotEmpty(start) && ObjUtil.isNotEmpty(end), TfsDayFillPO::getFillDate, start, end)
                .orderByDesc(TfsDayFillPO::getFillDate);
        return selectList(wrapper);
    }

    List<TfsDayFillPO> selectByLast(String openId);
}
