package cn.genn.orch.tfs.application.client;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.orch.tfs.application.dto.holiday.HolidayDTO;
import cn.genn.orch.tfs.application.dto.holiday.HolidayResponse;
import cn.genn.orch.tfs.application.dto.robot.RobotRequest;
import cn.genn.orch.tfs.application.dto.robot.RobotRequestBuilder;
import cn.genn.orch.tfs.infrastructure.constant.CacheConstants;
import cn.genn.orch.tfs.infrastructure.exception.MessageCode;
import cn.genn.orch.tfs.infrastructure.utils.FeishuInvokeUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.attendance.v1.model.QueryUserApprovalReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserApprovalReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserApprovalRespBody;
import com.lark.oapi.service.attendance.v1.model.UserApproval;
import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.*;
import com.lark.oapi.service.im.v1.enums.CreateMessageReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageRespBody;
import com.lark.oapi.service.wiki.v2.enums.ObjTypeEnum;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeishuAppClient {

    @Resource
    private Client feishuClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    private static final int PAGE_SIZE = 100;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 获取多维表格指定数据表数据内容
     * 数据量大时,请注意性能和内存
     */
    public List<AppTableRecord> getWikRecordList(String wikToken, String tableId, FilterInfo filter) {
        //获取app_token
        String appToken = this.getWikTableData(wikToken, null);
        //循环获取表数据
        List<AppTableRecord> records = new ArrayList<>();
        SearchAppTableRecordRespBody wikRecordPage = this.getWikRecordPage(appToken, tableId, filter, null, PAGE_SIZE);
        if (wikRecordPage.getItems().length != 0) {
            records.addAll(Arrays.asList(wikRecordPage.getItems()));
        }
        while (wikRecordPage.getHasMore()) {
            String pageToken = wikRecordPage.getPageToken();
            wikRecordPage = this.getWikRecordPage(appToken, tableId, filter, pageToken, PAGE_SIZE);
            if (wikRecordPage.getItems().length != 0) {
                records.addAll(Arrays.asList(wikRecordPage.getItems()));
            }
        }
        return records;
    }

    /**
     * 获取飞书多维表格节点数据,主要用于获取objToken
     *
     * @param wikToken
     * @param objType
     * @return
     */
    public String getWikTableData(String wikToken, ObjTypeEnum objType) {
        //缓存
        String cacheKey = CacheConstants.getCacheWikAppToken(wikToken);
        String cachedUserInfo = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotEmpty(cachedUserInfo)) {
            return cachedUserInfo;
        }
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
                .token(wikToken)
                .objType(ObjUtil.isNull(objType) ? null : objType.getValue())
                .build();
        // 发起请求
        GetNodeSpaceRespBody respBody = FeishuInvokeUtil.executeRequest(req, feishuClient.wiki().v2().space()::getNode, MessageCode.FEISHU_GET_WIK_FAIL);
        String objToken = respBody.getNode().getObjToken();
        stringRedisTemplate.opsForValue().set(cacheKey, objToken, 100, TimeUnit.MINUTES);
        return objToken;
    }

    public SearchAppTableRecordRespBody getWikRecordPage(String objToken, String tableId, FilterInfo filter, String pageToken, Integer pageSize) {
        SearchAppTableRecordReq req = SearchAppTableRecordReq.newBuilder()
                .appToken(objToken)
                .tableId(tableId)
                .userIdType("open_id")
                .pageToken(pageToken)
                .pageSize(pageSize)
                .searchAppTableRecordReqBody(SearchAppTableRecordReqBody.newBuilder()
                        .filter(filter)
                        .automaticFields(true)
                        .build())
                .build();
        // 发起请求
        log.info("filter:{}", JsonUtils.toJson(filter));
        return FeishuInvokeUtil.executeRequest(req, feishuClient.bitable().v1().appTableRecord()::search, MessageCode.FEISHU_GET_WIK_DATE_FAIL);
    }

    public Map<String, Integer> getOverTimeCache(List<String> openIds, LocalDate startTime, LocalDate endTime) {
        Map<String, Integer> resultMap = new HashMap<>();
        List<String> notCachedOpenIds = new ArrayList<>();

        // 从Redis中获取用户信息
        for (String openId : openIds) {
            String key = openId + "_" + startTime.format(DATE_TIME_FORMATTER2) + "_" + endTime.format(DATE_TIME_FORMATTER2);
            String cacheKey = CacheConstants.getCacheUserOverTime(key);
            String cachedUserInfo = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotEmpty(cachedUserInfo)) {
                resultMap.put(openId, Integer.parseInt(cachedUserInfo));
            } else {
                notCachedOpenIds.add(openId);
            }
        }
        // 从飞书中获取未缓存的用户信息
        if (!notCachedOpenIds.isEmpty()) {
            List<List<String>> partition = Lists.partition(notCachedOpenIds, 40);
            for (List<String> list : partition) {
                Map<String, Integer> workHourMap = this.getWorkHour(list, startTime, endTime);
                for (String openId : workHourMap.keySet()) {
                    String key = openId + "_" + startTime.format(DATE_TIME_FORMATTER2) + "_" + endTime.format(DATE_TIME_FORMATTER2);
                    String cacheKey = CacheConstants.getCacheUserOverTime(key);
//                    stringRedisTemplate.opsForValue().set(cacheKey, workHourMap.get(openId).toString(), 1, TimeUnit.MINUTES);
                }
                resultMap.putAll(workHourMap);
            }
        }
        return resultMap;
    }

    /**
     * 获取用户指定时间范围内的工时,时间范围小于30天
     *
     * @param openIds
     * @return
     */
    public Map<String, Integer> getWorkHour(List<String> openIds, LocalDate startTime, LocalDate endTime) {
        int workDay = this.getUserOpenIdList(startTime, endTime);
        Map<String, Integer> overtime = this.getOverTime(openIds, startTime, endTime);
        for (String key : overtime.keySet()) {
            overtime.compute(key, (k, i) -> Math.max(workDay * 8 + i, 0));
        }
        return overtime;
    }

    /**
     * 获取时间范围内有几个工作日
     *
     * @return
     */
    private int getUserOpenIdList(LocalDate startTime, LocalDate endTime) {
        int workDay = 0;
        Map<String, Map<String, HolidayDTO>> holidayDataMap = new HashMap<>();
        for (LocalDate date = startTime; !date.isAfter(endTime); date = date.plusDays(1)) {
            //先根据节假日数据处理
            String yearMonth = date.format(DateTimeFormatter.ofPattern("yyyy-MM")); // 格式化为 "2025-05"
            Map<String, HolidayDTO> holidayDTOMap = null;
            if (!holidayDataMap.containsKey(yearMonth)) {
                try {
                    List<HolidayDTO> holidayData = this.getHolidayData(yearMonth);
                    holidayDTOMap = holidayData.stream().collect(Collectors.toMap(HolidayDTO::getDate, Function.identity()));
                    holidayDataMap.put(yearMonth, holidayDTOMap);
                } catch (Exception e) {
                    return 5;
                }
            } else {
                holidayDTOMap = holidayDataMap.get(yearMonth);
            }
            HolidayDTO holidayDTO = holidayDTOMap.get(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            if (ObjUtil.isNotNull(holidayDTO)) {
                if (!holidayDTO.getHoliday()) {
                    workDay++;
                }
            } else {
                //未获取到节假日根据周几来判断
                DayOfWeek dayOfWeek = date.getDayOfWeek();
                if (dayOfWeek.getValue() >= DayOfWeek.MONDAY.getValue() && dayOfWeek.getValue() <= DayOfWeek.FRIDAY.getValue()) {
                    workDay++;
                }
            }
        }
        return workDay;
    }

    private List<HolidayDTO> getHolidayData(String yearMonth) {
        String cacheKey = CacheConstants.getCacheHoliday(yearMonth);
        String cachedHoliday = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotEmpty(cachedHoliday)) {
            return JsonUtils.parseToList(cachedHoliday, HolidayDTO.class);
        }
        String json = null;
        try {
            HttpRequest httpRequest = HttpRequest.get("http://timor.tech/api/holiday/year/" + yearMonth);
            json = httpRequest.execute().body();
        } catch (Exception e) {
            log.error("获取{}假期数据失败: {}", yearMonth, e.getMessage());
            throw new BusinessException("获取假期数据失败:" + JsonUtils.toJson(json));
        }
        HolidayResponse resp = JsonUtils.parse(json, HolidayResponse.class);
        if (resp.getCode() != 0) {
            throw new BusinessException("获取假期数据失败:" + JsonUtils.toJson(resp));
        }
        List<HolidayDTO> holidayList = resp.getHolidayList();
        stringRedisTemplate.opsForValue().set(cacheKey, JsonUtils.toJson(holidayList), 7, TimeUnit.DAYS);
        return holidayList;
    }

    /**
     * 获取请假时间和加班时间的差值,例如,请假两天,加班一天,则返回-8h
     *
     * @param openIds   todo:不知道飞书有没有限制数量
     * @param startTime
     * @param endTime
     * @return
     */
    private Map<String, Integer> getOverTime(List<String> openIds, LocalDate startTime, LocalDate endTime) {
        QueryUserApprovalReq req = QueryUserApprovalReq.newBuilder()
                .employeeType("open_id")
                .queryUserApprovalReqBody(QueryUserApprovalReqBody.newBuilder()
                        .userIds(openIds.toArray(new String[0]))
                        .checkDateFrom(Integer.parseInt(startTime.format(DATE_TIME_FORMATTER2)))
                        .checkDateTo(Integer.parseInt(endTime.format(DATE_TIME_FORMATTER2)))
                        .build())
                .build();
        QueryUserApprovalRespBody queryUserApprovalRespBody = FeishuInvokeUtil.executeRequest(req, feishuClient.attendance().v1().userApproval()::query, MessageCode.OPEN_DATA_ERROR);
        Map<String, Integer> overtimeMap = new HashMap<>();
        if (ObjUtil.isNotNull(queryUserApprovalRespBody) || queryUserApprovalRespBody.getUserApprovals().length != 0) {
            for (UserApproval userApproval : queryUserApprovalRespBody.getUserApprovals()) {
                //处理加班和请假数据
                int sum = FeishuAppHelper.getOvertimeHour(userApproval.getOvertimeWorks()) - FeishuAppHelper.getEffectiveLeaveHours(userApproval.getLeaves());
                overtimeMap.put(userApproval.getUserId(), sum);
            }
        }
        for (String openId : openIds) {
            if (!overtimeMap.containsKey(openId)) {
                overtimeMap.put(openId, 0);
            }
        }
        return overtimeMap;
    }

    /**
     * 发送卡片消息,只支持模版卡片
     * <a href="https://open.feishu.cn/cardkit/editor">...</a>
     *
     * @return
     */
    public CreateMessageRespBody sendCard(String openId, String templateId, Map<String, Object> templateVariable) {
        RobotRequest request = RobotRequestBuilder.CardTemplate.create().template(templateId).variable(templateVariable).build();
        return this.sendMessage(openId, "interactive", JsonUtils.toJson(request.getCard()));
    }

    /**
     * 发送文本消息
     * @param openId
     * @param content
     */
    public CreateMessageRespBody sendText(String openId, String content){
        return this.sendMessage(openId, "text", String.format("{\"text\":\"%s\"}", content));
    }

    public CreateMessageRespBody sendMessage(String openId, String msgType, String content) {
        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(CreateMessageReceiveIdTypeEnum.OPEN_ID)
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(openId)
                        .msgType(msgType)
                        .content(content).build()).build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.im().message()::create, MessageCode.SEND_MESSAGE_FAIL);
    }

    public BatchCreateAppTableRecordRespBody addBatchWikRecord(String appToken, String tableId, List<HashMap<String, Object>> fields) {
        BatchCreateAppTableRecordReq req = BatchCreateAppTableRecordReq.newBuilder()
                .appToken(appToken)
                .tableId(tableId)
                .userIdType("open_id")
                .batchCreateAppTableRecordReqBody(BatchCreateAppTableRecordReqBody.newBuilder()
                        .records(fields.stream()
                                .map(field -> AppTableRecord.newBuilder().fields(field).build()).toArray(AppTableRecord[]::new))
                        .build())
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.bitable().v1().appTableRecord()::batchCreate, MessageCode.WIK_ADD_RECORD_FAIL);
    }

    /**
     * 添加多维表格记录
     */
    public CreateAppTableRecordRespBody addWikRecord(String appToken, String tableId, HashMap<String, Object> fields) {
        // 创建请求对象
        CreateAppTableRecordReq req = CreateAppTableRecordReq.newBuilder()
                .appToken(appToken)
                .tableId(tableId)
                .userIdType("open_id")
                .appTableRecord(AppTableRecord.newBuilder()
                        .fields(fields)
                        .build())
                .build();
        CreateAppTableRecordRespBody respBody = FeishuInvokeUtil.executeRequest(req, feishuClient.bitable().v1().appTableRecord()::create, MessageCode.WIK_ADD_RECORD_FAIL);
        return respBody;
    }

    /**
     * 编辑多维表格记录
     */
    public UpdateAppTableRecordRespBody updateWikRecord(String appToken, String tableId, String recordId, HashMap<String, Object> fields) {
        // 创建请求对象
        UpdateAppTableRecordReq req = UpdateAppTableRecordReq.newBuilder()
                .appToken(appToken)
                .tableId(tableId)
                .recordId(recordId)
                .userIdType("open_id")
                .appTableRecord(AppTableRecord.newBuilder()
                        .fields(fields)
                        .build())
                .build();
        // 发起请求
        UpdateAppTableRecordRespBody respBody = FeishuInvokeUtil.executeRequest(req, feishuClient.bitable().v1().appTableRecord()::update, MessageCode.WIK_UPDATE_RECORD_FAIL);
        return respBody;
    }

    /**
     * 批量删除多维表格
     */
    public Boolean removeWikRecord(String appToken, String tableId, List<String> recordIds) {
        BatchDeleteAppTableRecordReq req = BatchDeleteAppTableRecordReq.newBuilder()
                .appToken(appToken)
                .tableId(tableId)
                .batchDeleteAppTableRecordReqBody(BatchDeleteAppTableRecordReqBody.newBuilder()
                        .records(recordIds.toArray(new String[0]))
                        .build())
                .build();

        // 发起请求
        FeishuInvokeUtil.executeRequest(req, feishuClient.bitable().v1().appTableRecord()::batchDelete, MessageCode.WIK_DELETE_RECORD_FAIL);
        return true;
    }

    /**
     * 手机号获取用户openId
     * @param telephones
     * @return Map<手机号, openId>
     */
    public List<UserContactInfo> getOpenIdByTelephone(List<String> telephones) {
        if (CollUtil.isEmpty(telephones)) {
            throw new BusinessException(MessageCode.TELEPHONE_NOT_EXIST);
        }
        BatchGetIdUserReq req = BatchGetIdUserReq.newBuilder()
                .userIdType("open_id")
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                        .mobiles(telephones.toArray(new String[0]))
                        .includeResigned(true)
                        .build())
                .build();
        BatchGetIdUserRespBody respBody = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().user()::batchGetId, MessageCode.GET_OPEN_ID_FAIL);
        return Arrays.asList(respBody.getUserList());
    }

    /**
     * openIds获取用户信息
     * @param openIds
     * @return
     */
    public List<User> getUserBatch(List<String> openIds) {
        BatchUserReq req = BatchUserReq.newBuilder()
                .userIdType("open_id")
                .userIds(openIds.toArray(new String[0]))
                .build();
        BatchUserRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::batch, MessageCode.SEARCH_USER_INFO_FAIL);
        return Arrays.asList(body.getItems());
    }

    /**
     * 获取企业通讯录一级部门ids
     */
    public List<String> getContactScope() {
        ListScopeReq req = ListScopeReq.newBuilder().pageSize(100).build();
        ListScopeRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().scope()::list, MessageCode.CONTACT_INFO_FAIL);
        List<String> resultList = Arrays.asList(body.getDepartmentIds());
        while (resultList.size() == 100) {
            req = ListScopeReq.newBuilder().pageSize(100).pageToken(body.getPageToken()).build();
            body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().scope()::list, MessageCode.CONTACT_INFO_FAIL);
            resultList.addAll(Arrays.asList(body.getDepartmentIds()));
        }
        return resultList;
    }

    /**
     * 获取子部门列表
     */
    public List<Department> getDepartmentChildren(String departmentId) {
        List<Department> resultList = new ArrayList<>();
        ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        ChildrenDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::children, MessageCode.CHILDREN_DEPARTMENT_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList = Arrays.asList(body.getItems());
            while (resultList.size() == 50) {
                req = ChildrenDepartmentReq.newBuilder().pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::children, MessageCode.CHILDREN_DEPARTMENT_INFO_FAIL);
                resultList.addAll(Arrays.asList(body.getItems()));
            }
        }
        return resultList;
    }

    /**
     * 获取部门下用户信息
     */
    public List<User> getUserListByDepartment(String departmentId) {
        List<User> resultList = new ArrayList<>();
        FindByDepartmentUserReq req = FindByDepartmentUserReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        FindByDepartmentUserRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::findByDepartment, MessageCode.USER_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList.addAll(Arrays.asList(body.getItems()));
            while (resultList.size() == 50) {
                req = FindByDepartmentUserReq.newBuilder().departmentId(departmentId).pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::findByDepartment, MessageCode.USER_INFO_FAIL);
                resultList.addAll(Arrays.asList(body.getItems()));
            }
        }
        return resultList;
    }

    /**
     * openId获取全部父部门信息,子部门在前
     */
    public List<Department> fetchDepartments(String DepartmentId) {
        List<Department> resultList = new ArrayList<>();
        ParentDepartmentReq req = ParentDepartmentReq.newBuilder()
                .departmentId(DepartmentId)
                .pageSize(20)
                .build();
        ParentDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().department()::parent, MessageCode.PARENT_DEPARTMENT_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            return Arrays.asList(body.getItems());
        }
        return resultList;
    }



    /**
     * 获取应用权限范围内所有部门
     * @return
     */
    public List<String> getDepartmentAll(){
        List<String> departmentIds = this.getContactScope();
        if(CollUtil.isEmpty(departmentIds)){
            return null;
        }
        return getDepartments(departmentIds);
    }

    /**
     * 获取部门下所有用户
     * @param departmentIds
     * @return
     */
    public List<User> getDepartmentUserList(List<String> departmentIds) {
        List<User> userList = new ArrayList<>();
        for (String departmentId : departmentIds) {
            List<User> userListByDepartment = this.getUserListByDepartment(departmentId);
            if (CollUtil.isNotEmpty(userListByDepartment)) {
                userList.addAll(userListByDepartment);
            }
        }
        return userList;
    }

    private List<String> getDepartments(List<String> departmentIds) {
        Set<String> departmentIdAlls = new HashSet<>(departmentIds);
        for (String departmentId : departmentIds) {
            List<Department> departmentChildren = this.getDepartmentChildren(departmentId);
            if (CollUtil.isNotEmpty(departmentChildren)) {
                List<String> list = departmentChildren.stream().map(Department::getOpenDepartmentId).distinct().collect(Collectors.toList());
                departmentIdAlls.addAll(list);
                departmentIdAlls.addAll(getDepartments(list));
            }
        }
        return new ArrayList<>(departmentIdAlls);
    }


}
