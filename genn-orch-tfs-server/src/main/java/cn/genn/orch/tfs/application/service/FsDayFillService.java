package cn.genn.orch.tfs.application.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.client.FeishuAppHelper;
import cn.genn.orch.tfs.application.command.FillSaveDayCommand;
import cn.genn.orch.tfs.application.command.FillSaveDayOneCheckCommand;
import cn.genn.orch.tfs.application.dto.day.FIllDayDTO;
import cn.genn.orch.tfs.application.dto.day.FillDayValueDTO;
import cn.genn.orch.tfs.application.query.DropDayQuery;
import cn.genn.orch.tfs.infrastructure.constant.CacheConstants;
import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import cn.genn.orch.tfs.infrastructure.exception.MessageCode;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillLogMapper;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillMapper;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FsDayFillService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private TfsProperties tfsProperties;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TfsDayFillMapper tfsDayFillMapper;
    @Resource
    private TfsDayFillLogMapper tfsDayFillLogMapper;

    public FIllDayDTO queryDayFill(DropDayQuery query) {
        FIllDayDTO fillDTO = new FIllDayDTO(query);
        CompletableFuture<Integer> weekTimeFuture = CompletableFuture.supplyAsync(() -> feishuAppClient.getOverTimeCache(Collections.singletonList(query.getOpenId()), query.getDay(), query.getDay()).get(query.getOpenId()))
                .exceptionally(ex -> {
                    log.error("获取日总工时异常", ex);
                    return 0;
                });
        CompletableFuture<List<String>> dropDataListFuture = CompletableFuture.supplyAsync(() -> getUserDropDataList(query))
                .exceptionally(ex -> {
                    log.error("处理下拉框数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<List<FillDayValueDTO>> fillValuesFuture = CompletableFuture.supplyAsync(() -> getFillValuesList(query))
                .exceptionally(ex -> {
                    log.error("处理已填报数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(weekTimeFuture, dropDataListFuture, fillValuesFuture);
        try {
            allFutures.join();
            fillDTO.setDayTime(weekTimeFuture.get());
            List<String> dropDataDTOS = dropDataListFuture.get();
            fillDTO.setDropDataList(dropDataDTOS);
            List<FillDayValueDTO> fillValueDTOS = fillValuesFuture.get();
//            fillValueDTOS = this.filterOldData(fillValueDTOS,dropDataDTOS);
            fillDTO.setFillValues(fillValueDTOS);
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            throw new BusinessException(MessageCode.QUERY_FILL_DATA_ERROR);
        }
        return fillDTO;
    }

    /**
     * 日填报内容
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDayFill(FillSaveDayCommand command) {
        TfsDayFillLogPO tfsDayFillLogPO = tfsDayFillLogMapper.selectByOpenIdAndDay(command.getOpenId(), command.getDay());
        if (ObjUtil.isNull(tfsDayFillLogPO)) {
            throw new BusinessException("未找到发送记录:command:" + command.getOpenId() + "day:" + JsonUtils.toJson(command.getDay()));
        }
        if (tfsDayFillLogPO.getStatus().equals(StatusEnum.PROJECT)) {
            tfsDayFillLogPO.setStatus(StatusEnum.DEMAND);
            tfsDayFillLogMapper.updateById(tfsDayFillLogPO);
        }
        tfsDayFillMapper.deleteByOpenIdAndDay(command.getOpenId(), command.getDay());
        for (FillDayValueDTO fillDayValueDTO : command.getFillValues()) {
            TfsDayFillPO po = new TfsDayFillPO()
                    .setFillDate(command.getDay())
                    .setOpenId(command.getOpenId())
                    .setName(tfsDayFillLogPO.getName())
                    .setTelephone(tfsDayFillLogPO.getTelephone())
                    .setProjectName(fillDayValueDTO.getProjectName())
                    .setDepartments(tfsDayFillLogPO.getDepartments())
                    .setWorkHour(fillDayValueDTO.getWorkHour());
            tfsDayFillMapper.insert(po);
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDayFillOneCheck(FillSaveDayOneCheckCommand command) {

        //已填报的不管
        TfsDayFillLogPO tfsDayFillLogPO = tfsDayFillLogMapper.selectByOpenIdAndDay(command.getOpenId(), command.getDay());
        if(ObjUtil.isNull(tfsDayFillLogPO)){
            feishuAppClient.sendText(command.getOpenId(), "❌ 请联系开发人员,未查到发送记录!");
        }
        if(tfsDayFillLogPO.getStatus().equals(StatusEnum.DEMAND)){
            feishuAppClient.sendText(command.getOpenId(), "❌ 已填报,请勿重复填报!");
            return false;
        }
        //上次也没填的不管
        TfsDayFillLogPO lastPO = tfsDayFillLogMapper.selectLastByOpenIdAndNoDay(command.getOpenId(), command.getDay());
        if(ObjUtil.isNull(lastPO)){
            feishuAppClient.sendText(command.getOpenId(), "❌ 未获取到上次填报记录");
            return false;
        }
        if(lastPO.getStatus().equals(StatusEnum.PROJECT)){
            feishuAppClient.sendText(command.getOpenId(), "❌ 检测到上次的也未填报!");
            return false;
        }

        //修改填报记录
        List<TfsDayFillPO> tfsDayFillPOS = tfsDayFillMapper.selectByOpenIdAndDay(command.getOpenId(), lastPO.getSendDate());
        if(CollUtil.isEmpty(tfsDayFillPOS)){
            feishuAppClient.sendText(command.getOpenId(), "❌ 请联系开发人员,有填报记录,但未发现填报数据!");
            return false;
        }
        Integer hour = feishuAppClient.getOverTimeCache(Collections.singletonList(command.getOpenId()), command.getDay(), command.getDay()).get(command.getOpenId());
        List<TfsDayFillPO> tfsDayFillPOS1 = generateTodayFillRecords(tfsDayFillPOS, command.getDay(), hour);
        tfsDayFillPOS1.forEach(tfsDayFillMapper::insert);
        String content = generateProjectHoursDescription(tfsDayFillPOS1);
        feishuAppClient.sendText(command.getOpenId(), "✅ 填报成功:"+ content);

        tfsDayFillLogPO.setStatus(StatusEnum.DEMAND);
        tfsDayFillLogMapper.updateById(tfsDayFillLogPO);
        return true;
    }

    /**
     * 根据上次填报记录生成今日填报记录
     *
     * @param lastFillRecords 上次填报记录列表
     * @param targetDate 目标填报日期
     * @param totalHours 今日总工时
     * @return 生成的今日填报记录列表
     */
    public static List<TfsDayFillPO> generateTodayFillRecords(List<TfsDayFillPO> lastFillRecords,
                                                              LocalDate targetDate,
                                                              Integer totalHours) {
        if (CollUtil.isEmpty(lastFillRecords) || totalHours == null || totalHours <= 0) {
            return new ArrayList<>();
        }

        // 计算上次总工时
        float lastTotalHours = lastFillRecords.stream()
                .map(TfsDayFillPO::getWorkHour)
                .filter(Objects::nonNull)
                .reduce(0.0f, Float::sum);

        if (lastTotalHours <= 0) {
            return new ArrayList<>();
        }

        // 按项目分组计算工时
        Map<String, Float> projectHours = lastFillRecords.stream()
                .collect(Collectors.groupingBy(
                        TfsDayFillPO::getProjectName,
                        Collectors.summingDouble(r -> r.getWorkHour() != null ? r.getWorkHour() : 0.0f)
                )).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().floatValue()
                ));

        List<TfsDayFillPO> result = new ArrayList<>();
        TfsDayFillPO template = lastFillRecords.get(0);
        float allocatedHours = 0.0f;

        List<String> projects = new ArrayList<>(projectHours.keySet());
        for (int i = 0; i < projects.size(); i++) {
            String projectName = projects.get(i);
            float lastProjectHours = projectHours.get(projectName);

            float newHours;
            if (i == projects.size() - 1) {
                // 最后一个项目分配剩余工时
                newHours = totalHours - allocatedHours;
            } else {
                // 按比例计算并调整为0.5的倍数
                float ratio = lastProjectHours / lastTotalHours;
                float calculatedHours = totalHours * ratio;
                newHours = Math.round(calculatedHours * 2) / 2.0f; // 调整为0.5的倍数
                allocatedHours += newHours;
            }

            // 确保工时为0.5的倍数且大于0
            newHours = Math.max(0.5f, Math.round(newHours * 2) / 2.0f);

            if (newHours > 0) {
                TfsDayFillPO newRecord = new TfsDayFillPO()
                        .setFillDate(targetDate)
                        .setOpenId(template.getOpenId())
                        .setName(template.getName())
                        .setTelephone(template.getTelephone())
                        .setProjectName(projectName)
                        .setWorkHour(newHours)
                        .setDepartments(template.getDepartments());
                result.add(newRecord);
            }
        }

        return result;
    }

    /**
     * 根据填报记录生成项目工时描述字符串
     *
     * @param fillRecords 填报记录列表
     * @return 项目工时描述字符串
     */
    /**
     * 根据填报记录生成项目工时描述字符串
     *
     * @param fillRecords 填报记录列表
     * @return 项目工时描述字符串
     */
    public static String generateProjectHoursDescription(List<TfsDayFillPO> fillRecords) {
        if (CollUtil.isEmpty(fillRecords)) {
            return "暂无填报记录";
        }

        // 按项目分组并汇总工时
        Map<String, Float> projectHours = fillRecords.stream()
                .collect(Collectors.groupingBy(
                        TfsDayFillPO::getProjectName,
                        LinkedHashMap::new, // 保持顺序
                        Collectors.summingDouble(r -> r.getWorkHour() != null ? r.getWorkHour() : 0.0f)
                )).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().floatValue(),
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));

        // 生成描述字符串
        StringBuilder description = new StringBuilder();
        int index = 1;
        for (Map.Entry<String, Float> entry : projectHours.entrySet()) {
            if (description.length() > 0) {
                description.append(";");  // 改为换行
            }

            String projectName = entry.getKey();
            Float hours = entry.getValue();

            // 格式化工时显示（如果是整数就不显示小数点）
            String hoursStr = hours % 1 == 0 ?
                    String.valueOf(hours.intValue()) :
                    String.format("%.1f", hours);

            description.append(String.format("%d.%s %s小时", index++, projectName, hoursStr));
        }

        return description.toString();
    }


    private List<String> getUserDropDataList(DropDayQuery query) {

        String cacheKey = CacheConstants.getProjectList();
        String projectList = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotEmpty(projectList)) {
            return JsonUtils.parseToList(projectList, String.class);
        } else {
            String oneWayWikToken = tfsProperties.getWik().getOneWayWikToken();
            String oneWayWikTableId = tfsProperties.getWik().getOneWayWikTableId();
            List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(oneWayWikToken, oneWayWikTableId, null);
            List<String> resultList = new ArrayList<>();
            for (AppTableRecord appTableRecord : wikRecordList) {
                Map<String, Object> recordMap = appTableRecord.getFields();
                Object row1 = recordMap.get("业务线");
                if (ObjUtil.isNull(row1)) {
                    continue;
                }
                resultList.add(FeishuAppHelper.getDrop(row1));
            }
            if (CollUtil.isNotEmpty(resultList)) {
                resultList = resultList.stream().distinct().collect(Collectors.toList());
                stringRedisTemplate.opsForValue().set(cacheKey, JsonUtils.toJson(resultList), 5, TimeUnit.MINUTES);
            }
            return resultList;
        }
    }

    private List<FillDayValueDTO> getFillValuesList(DropDayQuery query) {
        List<TfsDayFillPO> resultList = tfsDayFillMapper.selectByOpenIdAndDay(query.getOpenId(), query.getDay());
        List<FillDayValueDTO> collect = resultList.stream().map(po -> {
            FillDayValueDTO dto = new FillDayValueDTO();
            dto.setProjectName(po.getProjectName());
            dto.setWorkHour(po.getWorkHour());
            return dto;
        }).collect(Collectors.toList());
        return collect;
    }

}
