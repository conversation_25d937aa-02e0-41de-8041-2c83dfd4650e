package cn.genn.orch.tfs.application.job;

import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.orch.tfs.application.service.FsDayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 定时提醒昨日未填报日报的人员
 */
@Slf4j
@Component
public class RemindUnfilledTask extends AbstractJobHandler {

    @Resource
    private FsDayNotifyService fsDayNotifyService;

    @Override
    public void doExecute() {
        log.info("定时提醒昨日未填报日报的人员 开始执行");
        long start = System.currentTimeMillis();

        LocalDate yesterday = LocalDate.now().minusDays(1);
        fsDayNotifyService.remindUnfilledUsers(yesterday);
        
        log.info("定时提醒昨日未填报日报的人员,执行耗时:{}s", (System.currentTimeMillis() - start) / 1000);
    }
}
