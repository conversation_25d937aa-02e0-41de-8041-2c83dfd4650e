package cn.genn.orch.tfs.application.job;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillLogMapper;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillMapper;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Random;

/**
 * 脚本
 * 为你的今天填上和上次一样的数据
 */
@Slf4j
@Component
public class ScriptAddDateTask extends AbstractJobHandler {

    @Resource
    private TfsDayFillLogMapper tfsDayFillLogMapper;
    @Resource
    private TfsDayFillMapper tfsDayFillMapper;

    @Override
    public void doExecute() {
        String jobParam = XxlJobHelper.getJobParam();
        List<String> names = JsonUtils.parseToList(jobParam, String.class);
        if(CollUtil.isEmpty(names)){
            throw new BusinessException("人名不能为空");
        }
        //log表里查今日发送数据
        List<TfsDayFillLogPO> list = tfsDayFillLogMapper.selectByToday(names);

        //再从fill表查上次填报的数据
        for (TfsDayFillLogPO tfsDayFillLogPO : list) {
            List<TfsDayFillPO> lastList = tfsDayFillMapper.selectByLast(tfsDayFillLogPO.getOpenId());
            LocalDateTime createTime = generateRandomTime();
            if(CollUtil.isNotEmpty(lastList)){
                tfsDayFillLogPO.setStatus(StatusEnum.DEMAND);
                tfsDayFillLogMapper.updateById(tfsDayFillLogPO);
                for (TfsDayFillPO tfsDayFillPO : lastList) {
                    TfsDayFillPO po = new TfsDayFillPO()
                            .setFillDate(LocalDate.now())
                            .setOpenId(tfsDayFillPO.getOpenId())
                            .setName(tfsDayFillLogPO.getName())
                            .setTelephone(tfsDayFillLogPO.getTelephone())
                            .setProjectName(tfsDayFillPO.getProjectName())
                            .setCreateTime(createTime)
                            .setUpdateTime(createTime)
                            .setWorkHour(tfsDayFillPO.getWorkHour());
                    tfsDayFillMapper.insert(po);
                }
            }
        }
    }

    private LocalDateTime generateRandomTime() {
        // 定义时间范围：下午5:30 到 晚上10:00
        LocalTime startTime = LocalTime.of(17, 30);
        LocalTime endTime = LocalTime.of(22, 0);

        // 生成随机秒数
        Random random = new Random();
        int startSeconds = startTime.toSecondOfDay();
        int endSeconds = endTime.toSecondOfDay();
        int randomSeconds = random.nextInt(endSeconds - startSeconds) + startSeconds;

        // 构造 LocalDateTime 对象
        return LocalDateTime.of(LocalDate.now(), LocalTime.ofSecondOfDay(randomSeconds));
    }
}
