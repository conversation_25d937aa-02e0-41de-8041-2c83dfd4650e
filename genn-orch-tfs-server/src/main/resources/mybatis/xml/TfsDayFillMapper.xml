<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillMapper">

    <select id="selectByLast" resultType="cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO">
        SELECT *
        FROM tfs_day_fill
        WHERE open_id = #{openId}
          AND deleted = 0
          AND fill_date = (
              SELECT MAX(fill_date)
              FROM tfs_day_fill
              WHERE open_id = #{openId}
                AND deleted = 0
          )
        ORDER BY id
    </select>
</mapper>